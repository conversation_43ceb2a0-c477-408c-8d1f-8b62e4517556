esphome:
  name: myesphome8266
  on_boot:
    priority: -100  # 启动优先级（可选）
    then:
      - delay: 5s
      - logger.log: "系统上电后，已等待5秒。现在开始工作"
      - switch.turn_on: some_output
esp8266:
  board: nodemcuv2

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password



sensor:
# DHT11传感器 (保留原有配置)
- platform: dht
  pin: D3  # 改为D3引脚，因为D2现在用于I2C SCL
  temperature:
    name: "Bed Room Temperature DHT11"
  humidity:
    name: "Bed Room Humidity DHT11"
  update_interval: 60s
  model: DHT11

logger:
  baud_rate: 115200
ota:
  platform: esphome
api:

web_server:
  port: 80