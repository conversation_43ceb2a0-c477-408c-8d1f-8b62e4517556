{"storage_version": 1, "name": "myesp8266", "friendly_name": null, "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "myesp8266.local", "web_port": 80, "esp_platform": "ESP8266", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.pioenvs\\myesp8266\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "binary_sensor", "esp8266", "esphome", "globals", "interval", "json", "logger", "md5", "mdns", "network", "ota", "preferences", "restart", "safe_mode", "sensor", "socket", "status", "switch", "template", "text_sensor", "uart", "uptime", "web_server", "web_server_base", "wifi", "wifi_info", "wifi_signal"], "loaded_platforms": ["binary_sensor/status", "ota/esphome", "sensor/template", "sensor/uptime", "sensor/wifi_signal", "switch/restart", "text_sensor/wifi_info"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp8266"}