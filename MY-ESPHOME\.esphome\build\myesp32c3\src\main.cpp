// Auto generated code by esphome
// ========== AUTO GENERATED INCLUDE BLOCK BEGIN ===========
#include "esphome.h"
using namespace esphome;
using std::isnan;
using std::min;
using std::max;
using namespace sensor;
using namespace light;
using namespace text_sensor;
using namespace binary_sensor;
using namespace switch_;
logger::Logger *logger_logger_id;
web_server_base::WebServerBase *web_server_base_webserverbase_id;
wifi::WiFiComponent *wifi_wificomponent_id;
mdns::MDNSComponent *mdns_mdnscomponent_id;
esphome::ESPHomeOTAComponent *esphome_esphomeotacomponent_id;
safe_mode::SafeModeComponent *safe_mode_safemodecomponent_id;
Automation<> *automation_id;
LambdaAction<> *lambdaaction_id;
Automation<> *automation_id_2;
LambdaAction<> *lambdaaction_id_2;
api::APIServer *api_apiserver_id;
using namespace api;
web_server::WebServer *web_server_webserver_id;
const uint8_t ESPHOME_WEBSERVER_INDEX_HTML[174] PROGMEM = {60, 33, 68, 79, 67, 84, 89, 80, 69, 32, 104, 116, 109, 108, 62, 60, 104, 116, 109, 108, 62, 60, 104, 101, 97, 100, 62, 60, 109, 101, 116, 97, 32, 99, 104, 97, 114, 115, 101, 116, 61, 85, 84, 70, 45, 56, 62, 60, 108, 105, 110, 107, 32, 114, 101, 108, 61, 105, 99, 111, 110, 32, 104, 114, 101, 102, 61, 100, 97, 116, 97, 58, 62, 60, 47, 104, 101, 97, 100, 62, 60, 98, 111, 100, 121, 62, 60, 101, 115, 112, 45, 97, 112, 112, 62, 60, 47, 101, 115, 112, 45, 97, 112, 112, 62, 60, 115, 99, 114, 105, 112, 116, 32, 115, 114, 99, 61, 34, 104, 116, 116, 112, 115, 58, 47, 47, 111, 105, 46, 101, 115, 112, 104, 111, 109, 101, 46, 105, 111, 47, 118, 50, 47, 119, 119, 119, 46, 106, 115, 34, 62, 60, 47, 115, 99, 114, 105, 112, 116, 62, 60, 47, 98, 111, 100, 121, 62, 60, 47, 104, 116, 109, 108, 62};
const size_t ESPHOME_WEBSERVER_INDEX_HTML_SIZE = 174;
using namespace json;
preferences::IntervalSyncer *preferences_intervalsyncer_id;
using namespace uart;
uart::ESP32ArduinoUARTComponent *uart_bus;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id_2;
uart::UARTDebugger *uart_uartdebugger_id;
Automation<uart::UARTDirection, std::vector<uint8_t>> *automation_id_3;
LambdaAction<uart::UARTDirection, std::vector<uint8_t>> *lambdaaction_id_3;
template_::TemplateSensor *sht30_temp;
template_::TemplateSensor *sht30_hum;
wifi_signal::WiFiSignalSensor *wifi_signal_wifisignalsensor_id;
uptime::UptimeSecondsSensor *uptime_uptimesecondssensor_id;
internal_temperature::InternalTemperatureSensor *internal_temperature_internaltemperaturesensor_id;
interval::IntervalTrigger *interval_intervaltrigger_id;
Automation<> *automation_id_4;
status_led::StatusLEDLightOutput *status_led_statusledlightoutput_id;
esp32::ESP32InternalGPIOPin *esp32_esp32internalgpiopin_id_3;
light::LightState *light_lightstate_id;
wifi_info::SSIDWiFiInfo *wifi_info_ssidwifiinfo_id;
wifi_info::MacAddressWifiInfo *wifi_info_macaddresswifiinfo_id;
wifi_info::IPAddressWiFiInfo *wifi_info_ipaddresswifiinfo_id;
status::StatusBinarySensor *status_statusbinarysensor_id;
restart::RestartSwitch *restart_restartswitch_id;
using namespace output;
globals::GlobalsComponent<std::string> *uart_buffer;
LambdaAction<> *lambdaaction_id_4;
interval::IntervalTrigger *interval_intervaltrigger_id_2;
Automation<> *automation_id_5;
uart::UARTWriteAction<> *uart_uartwriteaction_id;
#define yield() esphome::yield()
#define millis() esphome::millis()
#define micros() esphome::micros()
#define delay(x) esphome::delay(x)
#define delayMicroseconds(x) esphome::delayMicroseconds(x)
// ========== AUTO GENERATED INCLUDE BLOCK END ==========="

void setup() {
  // ========== AUTO GENERATED CODE BEGIN ===========
  App.reserve_text_sensor(3);
  App.reserve_switch(1);
  App.reserve_sensor(5);
  App.reserve_light(1);
  App.reserve_binary_sensor(1);
  // network:
  //   enable_ipv6: false
  //   min_ipv6_addr_count: 0
  // async_tcp:
  //   {}
  // esphome:
  //   name: myesp32c3
  //   friendly_name: SHT30 ESP32-C3 Sensor
  //   min_version: 2025.7.2
  //   build_path: build\myesp32c3
  //   platformio_options: {}
  //   includes: []
  //   libraries: []
  //   name_add_mac_suffix: false
  //   debug_scheduler: false
  //   areas: []
  //   devices: []
  App.pre_setup("myesp32c3", "SHT30 ESP32-C3 Sensor", "", __DATE__ ", " __TIME__, false);
  App.reserve_components(27);
  // sensor:
  // light:
  // text_sensor:
  // binary_sensor:
  // switch:
  // logger:
  //   baud_rate: 115200
  //   level: DEBUG
  //   logs:
  //     sht30_uart: DEBUG
  //     uart_debug: INFO
  //     uart: DEBUG
  //   id: logger_logger_id
  //   tx_buffer_size: 512
  //   deassert_rts_dtr: false
  //   task_log_buffer_size: 768
  //   hardware_uart: USB_CDC
  logger_logger_id = new logger::Logger(115200, 512);
  logger_logger_id->create_pthread_key();
  logger_logger_id->init_log_buffer(768);
  logger_logger_id->set_log_level(ESPHOME_LOG_LEVEL_DEBUG);
  logger_logger_id->set_uart_selection(logger::UART_SELECTION_USB_CDC);
  logger_logger_id->pre_setup();
  logger_logger_id->set_log_level("sht30_uart", ESPHOME_LOG_LEVEL_DEBUG);
  logger_logger_id->set_log_level("uart_debug", ESPHOME_LOG_LEVEL_INFO);
  logger_logger_id->set_log_level("uart", ESPHOME_LOG_LEVEL_DEBUG);
  logger_logger_id->set_component_source("logger");
  App.register_component(logger_logger_id);
  // web_server_base:
  //   id: web_server_base_webserverbase_id
  web_server_base_webserverbase_id = new web_server_base::WebServerBase();
  web_server_base_webserverbase_id->set_component_source("web_server_base");
  App.register_component(web_server_base_webserverbase_id);
  web_server_base::global_web_server_base = web_server_base_webserverbase_id;
  // wifi:
  //   ap:
  //     ssid: ESP32C3-SHT30 Fallback Hotspot
  //     password: '12345678'
  //     id: wifi_wifiap_id
  //     ap_timeout: 1min
  //   on_connect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接成功！
  //           args: []
  //           level: DEBUG
  //           tag: main
  //         type_id: lambdaaction_id
  //     trigger_id: trigger_id
  //     automation_id: automation_id
  //   on_disconnect:
  //     then:
  //       - logger.log:
  //           format: WiFi连接断开！
  //           args: []
  //           level: DEBUG
  //           tag: main
  //         type_id: lambdaaction_id_2
  //     trigger_id: trigger_id_2
  //     automation_id: automation_id_2
  //   id: wifi_wificomponent_id
  //   domain: .local
  //   reboot_timeout: 15min
  //   power_save_mode: LIGHT
  //   fast_connect: false
  //   passive_scan: false
  //   enable_on_boot: true
  //   networks:
  //     - ssid: !secret 'wifi_ssid'
  //       password: !secret 'wifi_password'
  //       id: wifi_wifiap_id_2
  //       priority: 0.0
  //   use_address: myesp32c3.local
  wifi_wificomponent_id = new wifi::WiFiComponent();
  wifi_wificomponent_id->set_use_address("myesp32c3.local");
  {
  wifi::WiFiAP wifi_wifiap_id_2 = wifi::WiFiAP();
  wifi_wifiap_id_2.set_ssid("HOME");
  wifi_wifiap_id_2.set_password("nb9d30@24zd");
  wifi_wifiap_id_2.set_priority(0.0f);
  wifi_wificomponent_id->add_sta(wifi_wifiap_id_2);
  }
  {
  wifi::WiFiAP wifi_wifiap_id = wifi::WiFiAP();
  wifi_wifiap_id.set_ssid("ESP32C3-SHT30 Fallback Hotspot");
  wifi_wifiap_id.set_password("12345678");
  wifi_wificomponent_id->set_ap(wifi_wifiap_id);
  }
  wifi_wificomponent_id->set_ap_timeout(60000);
  wifi_wificomponent_id->set_reboot_timeout(900000);
  wifi_wificomponent_id->set_power_save_mode(wifi::WIFI_POWER_SAVE_LIGHT);
  wifi_wificomponent_id->set_fast_connect(false);
  wifi_wificomponent_id->set_passive_scan(false);
  wifi_wificomponent_id->set_enable_on_boot(true);
  wifi_wificomponent_id->set_component_source("wifi");
  App.register_component(wifi_wificomponent_id);
  // mdns:
  //   id: mdns_mdnscomponent_id
  //   disabled: false
  //   services: []
  mdns_mdnscomponent_id = new mdns::MDNSComponent();
  mdns_mdnscomponent_id->set_component_source("mdns");
  App.register_component(mdns_mdnscomponent_id);
  // ota:
  // ota.esphome:
  //   platform: esphome
  //   id: esphome_esphomeotacomponent_id
  //   version: 2
  //   port: 3232
  esphome_esphomeotacomponent_id = new esphome::ESPHomeOTAComponent();
  esphome_esphomeotacomponent_id->set_port(3232);
  esphome_esphomeotacomponent_id->set_component_source("esphome.ota");
  App.register_component(esphome_esphomeotacomponent_id);
  // safe_mode:
  //   id: safe_mode_safemodecomponent_id
  //   boot_is_good_after: 1min
  //   disabled: false
  //   num_attempts: 10
  //   reboot_timeout: 5min
  safe_mode_safemodecomponent_id = new safe_mode::SafeModeComponent();
  safe_mode_safemodecomponent_id->set_component_source("safe_mode");
  App.register_component(safe_mode_safemodecomponent_id);
  if (safe_mode_safemodecomponent_id->should_enter_safe_mode(10, 300000, 60000)) return;
  automation_id = new Automation<>(wifi_wificomponent_id->get_connect_trigger());
  lambdaaction_id = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\210\220\345\212\237\357\274\201");
  });
  automation_id->add_actions({lambdaaction_id});
  automation_id_2 = new Automation<>(wifi_wificomponent_id->get_disconnect_trigger());
  lambdaaction_id_2 = new LambdaAction<>([=]() -> void {
      ESP_LOGD("main", "WiFi\350\277\236\346\216\245\346\226\255\345\274\200\357\274\201");
  });
  automation_id_2->add_actions({lambdaaction_id_2});
  // api:
  //   id: api_apiserver_id
  //   port: 6053
  //   password: ''
  //   reboot_timeout: 15min
  //   batch_delay: 100ms
  //   custom_services: false
  api_apiserver_id = new api::APIServer();
  api_apiserver_id->set_component_source("api");
  App.register_component(api_apiserver_id);
  api_apiserver_id->set_port(6053);
  api_apiserver_id->set_reboot_timeout(900000);
  api_apiserver_id->set_batch_delay(100);
  // web_server:
  //   port: 80
  //   auth:
  //     username: admin
  //     password: !secret 'wifi_password'
  //   id: web_server_webserver_id
  //   version: 2
  //   enable_private_network_access: true
  //   web_server_base_id: web_server_base_webserverbase_id
  //   include_internal: false
  //   log: true
  //   css_url: ''
  //   js_url: https:oi.esphome.io/v2/www.js
  web_server_webserver_id = new web_server::WebServer(web_server_base_webserverbase_id);
  web_server_webserver_id->set_component_source("web_server");
  App.register_component(web_server_webserver_id);
  web_server_base_webserverbase_id->set_port(80);
  web_server_webserver_id->set_expose_log(true);
  web_server_base_webserverbase_id->set_auth_username("admin");
  web_server_base_webserverbase_id->set_auth_password("nb9d30@24zd");
  web_server_webserver_id->set_include_internal(false);
  // json:
  //   {}
  // esp32:
  //   board: airm2m_core_esp32c3
  //   framework:
  //     version: 3.1.3
  //     advanced:
  //       ignore_efuse_custom_mac: false
  //     source: pioarduino/framework-arduinoespressif32@https:github.com/espressif/arduino-esp32/releases/download/3.1.3/esp32-3.1.3.zip
  //     platform_version: https:github.com/pioarduino/platform-espressif32/releases/download/53.03.13/platform-espressif32.zip
  //     type: arduino
  //   flash_size: 4MB
  //   variant: ESP32C3
  //   cpu_frequency: 160MHZ
  setCpuFrequencyMhz(160);
  // preferences:
  //   id: preferences_intervalsyncer_id
  //   flash_write_interval: 60s
  preferences_intervalsyncer_id = new preferences::IntervalSyncer();
  preferences_intervalsyncer_id->set_write_interval(60000);
  preferences_intervalsyncer_id->set_component_source("preferences");
  App.register_component(preferences_intervalsyncer_id);
  // uart:
  //   id: uart_bus
  //   tx_pin:
  //     number: 0
  //     mode:
  //       output: true
  //       input: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   rx_pin:
  //     number: 1
  //     mode:
  //       input: true
  //       output: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id_2
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   baud_rate: 9600
  //   data_bits: 8
  //   parity: NONE
  //   stop_bits: 1
  //   rx_buffer_size: 512
  //   debug:
  //     direction: RX
  //     dummy_receiver: false
  //     trigger_id: uart_uartdebugger_id
  //     after:
  //       bytes: 150
  //       timeout: 100ms
  //     sequence:
  //       - then:
  //           - lambda: !lambda |-
  //               UARTDebug::log_hex(direction, bytes, ':');
  //             type_id: lambdaaction_id_3
  //         trigger_id: trigger_id_3
  //         automation_id: automation_id_3
  //     dummy_receiver_id: uart_uartdummyreceiver_id
  uart_bus = new uart::ESP32ArduinoUARTComponent();
  uart_bus->set_component_source("uart");
  App.register_component(uart_bus);
  uart_bus->set_baud_rate(9600);
  esp32_esp32internalgpiopin_id = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id->set_pin(::GPIO_NUM_0);
  esp32_esp32internalgpiopin_id->set_inverted(false);
  esp32_esp32internalgpiopin_id->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id->set_flags(gpio::Flags::FLAG_OUTPUT);
  uart_bus->set_tx_pin(esp32_esp32internalgpiopin_id);
  esp32_esp32internalgpiopin_id_2 = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id_2->set_pin(::GPIO_NUM_1);
  esp32_esp32internalgpiopin_id_2->set_inverted(false);
  esp32_esp32internalgpiopin_id_2->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id_2->set_flags(gpio::Flags::FLAG_INPUT);
  uart_bus->set_rx_pin(esp32_esp32internalgpiopin_id_2);
  uart_bus->set_rx_buffer_size(512);
  uart_bus->set_stop_bits(1);
  uart_bus->set_data_bits(8);
  uart_bus->set_parity(uart::UART_CONFIG_PARITY_NONE);
  uart_uartdebugger_id = new uart::UARTDebugger(uart_bus);
  uart_uartdebugger_id->set_component_source("uart");
  App.register_component(uart_uartdebugger_id);
  automation_id_3 = new Automation<uart::UARTDirection, std::vector<uint8_t>>(uart_uartdebugger_id);
  lambdaaction_id_3 = new LambdaAction<uart::UARTDirection, std::vector<uint8_t>>([=](uart::UARTDirection direction, std::vector<uint8_t> bytes) -> void {
      UARTDebug::log_hex(direction, bytes, ':');
  });
  automation_id_3->add_actions({lambdaaction_id_3});
  uart_uartdebugger_id->set_direction(uart::UART_DIRECTION_RX);
  uart_uartdebugger_id->set_after_bytes(150);
  uart_uartdebugger_id->set_after_timeout(100);
  // sensor.template:
  //   platform: template
  //   name: SHT30 UART Temperature
  //   id: sht30_temp
  //   unit_of_measurement: °C
  //   device_class: temperature
  //   state_class: measurement
  //   accuracy_decimals: 1
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  sht30_temp = new template_::TemplateSensor();
  App.register_sensor(sht30_temp);
  sht30_temp->set_name("SHT30 UART Temperature");
  sht30_temp->set_object_id("sht30_uart_temperature");
  sht30_temp->set_disabled_by_default(false);
  sht30_temp->set_device_class("temperature");
  sht30_temp->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sht30_temp->set_unit_of_measurement("\302\260C");
  sht30_temp->set_accuracy_decimals(1);
  sht30_temp->set_force_update(false);
  sht30_temp->set_update_interval(60000);
  sht30_temp->set_component_source("template.sensor");
  App.register_component(sht30_temp);
  // sensor.template:
  //   platform: template
  //   name: SHT30 UART Humidity
  //   id: sht30_hum
  //   unit_of_measurement: '%'
  //   device_class: humidity
  //   state_class: measurement
  //   accuracy_decimals: 1
  //   disabled_by_default: false
  //   force_update: false
  //   update_interval: 60s
  sht30_hum = new template_::TemplateSensor();
  App.register_sensor(sht30_hum);
  sht30_hum->set_name("SHT30 UART Humidity");
  sht30_hum->set_object_id("sht30_uart_humidity");
  sht30_hum->set_disabled_by_default(false);
  sht30_hum->set_device_class("humidity");
  sht30_hum->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  sht30_hum->set_unit_of_measurement("%");
  sht30_hum->set_accuracy_decimals(1);
  sht30_hum->set_force_update(false);
  sht30_hum->set_update_interval(60000);
  sht30_hum->set_component_source("template.sensor");
  App.register_component(sht30_hum);
  // sensor.wifi_signal:
  //   platform: wifi_signal
  //   name: WiFi Signal
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: wifi_signal_wifisignalsensor_id
  //   unit_of_measurement: dBm
  //   accuracy_decimals: 0
  //   device_class: signal_strength
  //   state_class: measurement
  //   entity_category: diagnostic
  wifi_signal_wifisignalsensor_id = new wifi_signal::WiFiSignalSensor();
  App.register_sensor(wifi_signal_wifisignalsensor_id);
  wifi_signal_wifisignalsensor_id->set_name("WiFi Signal");
  wifi_signal_wifisignalsensor_id->set_object_id("wifi_signal");
  wifi_signal_wifisignalsensor_id->set_disabled_by_default(false);
  wifi_signal_wifisignalsensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_signal_wifisignalsensor_id->set_device_class("signal_strength");
  wifi_signal_wifisignalsensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  wifi_signal_wifisignalsensor_id->set_unit_of_measurement("dBm");
  wifi_signal_wifisignalsensor_id->set_accuracy_decimals(0);
  wifi_signal_wifisignalsensor_id->set_force_update(false);
  wifi_signal_wifisignalsensor_id->set_update_interval(60000);
  wifi_signal_wifisignalsensor_id->set_component_source("wifi_signal.sensor");
  App.register_component(wifi_signal_wifisignalsensor_id);
  // sensor.uptime:
  //   platform: uptime
  //   name: Uptime
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: uptime_uptimesecondssensor_id
  //   unit_of_measurement: s
  //   icon: mdi:timer-outline
  //   accuracy_decimals: 0
  //   device_class: duration
  //   state_class: total_increasing
  //   entity_category: diagnostic
  //   type: seconds
  uptime_uptimesecondssensor_id = new uptime::UptimeSecondsSensor();
  App.register_sensor(uptime_uptimesecondssensor_id);
  uptime_uptimesecondssensor_id->set_name("Uptime");
  uptime_uptimesecondssensor_id->set_object_id("uptime");
  uptime_uptimesecondssensor_id->set_disabled_by_default(false);
  uptime_uptimesecondssensor_id->set_icon("mdi:timer-outline");
  uptime_uptimesecondssensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  uptime_uptimesecondssensor_id->set_device_class("duration");
  uptime_uptimesecondssensor_id->set_state_class(sensor::STATE_CLASS_TOTAL_INCREASING);
  uptime_uptimesecondssensor_id->set_unit_of_measurement("s");
  uptime_uptimesecondssensor_id->set_accuracy_decimals(0);
  uptime_uptimesecondssensor_id->set_force_update(false);
  uptime_uptimesecondssensor_id->set_update_interval(60000);
  uptime_uptimesecondssensor_id->set_component_source("uptime.sensor");
  App.register_component(uptime_uptimesecondssensor_id);
  // sensor.internal_temperature:
  //   platform: internal_temperature
  //   name: ESP32-C3 Internal Temperature
  //   update_interval: 60s
  //   disabled_by_default: false
  //   force_update: false
  //   id: internal_temperature_internaltemperaturesensor_id
  //   unit_of_measurement: °C
  //   accuracy_decimals: 1
  //   device_class: temperature
  //   state_class: measurement
  //   entity_category: diagnostic
  internal_temperature_internaltemperaturesensor_id = new internal_temperature::InternalTemperatureSensor();
  App.register_sensor(internal_temperature_internaltemperaturesensor_id);
  internal_temperature_internaltemperaturesensor_id->set_name("ESP32-C3 Internal Temperature");
  internal_temperature_internaltemperaturesensor_id->set_object_id("esp32-c3_internal_temperature");
  internal_temperature_internaltemperaturesensor_id->set_disabled_by_default(false);
  internal_temperature_internaltemperaturesensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  internal_temperature_internaltemperaturesensor_id->set_device_class("temperature");
  internal_temperature_internaltemperaturesensor_id->set_state_class(sensor::STATE_CLASS_MEASUREMENT);
  internal_temperature_internaltemperaturesensor_id->set_unit_of_measurement("\302\260C");
  internal_temperature_internaltemperaturesensor_id->set_accuracy_decimals(1);
  internal_temperature_internaltemperaturesensor_id->set_force_update(false);
  internal_temperature_internaltemperaturesensor_id->set_update_interval(60000);
  internal_temperature_internaltemperaturesensor_id->set_component_source("internal_temperature.sensor");
  App.register_component(internal_temperature_internaltemperaturesensor_id);
  // interval:
  //   - interval: 500ms
  //     then:
  //       - lambda: !lambda |-
  //            检查UART是否有可用数据
  //           int available = id(uart_bus).available();
  //           if (available > 0) {
  //             ESP_LOGI("sht30_uart", "UART缓冲区有 %d 字节可用数据", available);
  //   
  //              使用flush清空可能的错误状态
  //             id(uart_bus).flush();
  //   
  //              等待一小段时间让数据稳定
  //             delay(50);
  //   
  //              重新检查可用数据
  //             available = id(uart_bus).available();
  //             ESP_LOGI("sht30_uart", "flush后UART缓冲区有 %d 字节可用数据", available);
  //   
  //             if (available > 0) {
  //                尝试使用peek_byte先查看数据
  //               uint8_t peek_data;
  //               if (id(uart_bus).peek_byte(&peek_data)) {
  //                 ESP_LOGI("sht30_uart", "peek到第一个字节: 0x%02X ('%c')", peek_data,
  //                          (peek_data >= 32 && peek_data <= 126) ? (char)peek_data : '?');
  //               }
  //   
  //                尝试读取较小的数据块
  //               uint8_t data[64];   减小缓冲区大小
  //               size_t len = id(uart_bus).read_array(data, std::min(available, 64));
  //   
  //               ESP_LOGI("sht30_uart", "实际读取到 %d 字节数据", len);
  //   
  //               if (len > 0) {
  //                  打印原始字节数据（十六进制）
  //                 std::string hex_data = "";
  //                 std::string readable = "";
  //   
  //                 for (size_t i = 0; i < len; i++) {
  //                   char hex_char[4];
  //                   sprintf(hex_char, "%02X ", data[i]);
  //                   hex_data += hex_char;
  //   
  //                   char c = (char)data[i];
  //                   if (c >= 32 && c <= 126) {
  //                     readable += c;
  //                   } else if (c == '\r') {
  //                     readable += "\\r";
  //                   } else if (c == '\n') {
  //                     readable += "\\n";
  //                   } else {
  //                     char ctrl_char[6];
  //                     sprintf(ctrl_char, "\\x%02X", (uint8_t)c);
  //                     readable += ctrl_char;
  //                   }
  //                 }
  //   
  //                 ESP_LOGI("sht30_uart", "原始数据(HEX): %s", hex_data.c_str());
  //                 ESP_LOGI("sht30_uart", "可读数据: %s", readable.c_str());
  //   
  //                  将数据添加到缓冲区进行行处理
  //                 for (size_t i = 0; i < len; i++) {
  //                   char c = (char)data[i];
  //                   if (c == '\n' || c == '\r') {
  //                     if (!id(uart_buffer).empty()) {
  //                       std::string line = id(uart_buffer);
  //                       ESP_LOGI("sht30_uart", "完整行数据: '%s' (长度: %d)", line.c_str(), line.length());
  //   
  //                        检查是否是SHT30数据格式 - 实际格式是 :068.9RH 031.6C
  //                       if (line.find(":") == 0 && line.find("RH") != std::string::npos && line.find("C") != std::string::npos) {
  //                         ESP_LOGI("sht30_uart", "检测到SHT30数据格式，开始解析...");
  //   
  //                          解析数据格式：:068.9RH 031.6C
  //                          湿度在位置1-5 (068.9)
  //                          温度在位置9-13 (031.6)
  //   
  //                         bool parse_ok = true;
  //                         float hum = 0.0, temp = 0.0;
  //   
  //                          解析湿度 (位置1-5: "068.9")
  //                         if (line.length() >= 6) {
  //                           std::string hum_str = line.substr(1, 5);   跳过冒号，取5个字符
  //                           char* hum_end;
  //                           hum = strtof(hum_str.c_str(), &hum_end);
  //                           if (hum_end == hum_str.c_str()) parse_ok = false;
  //                         } else {
  //                           parse_ok = false;
  //                         }
  //   
  //                          解析温度 (位置9-13: "031.6")
  //                         if (parse_ok && line.length() >= 14) {
  //                           std::string temp_str = line.substr(9, 5);   从"RH "后开始，取5个字符
  //                           char* temp_end;
  //                           temp = strtof(temp_str.c_str(), &temp_end);
  //                           if (temp_end == temp_str.c_str()) parse_ok = false;
  //                         } else {
  //                           parse_ok = false;
  //                         }
  //   
  //                         if (parse_ok) {
  //                           ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);
  //   
  //                            更新传感器状态 - 发布到Home Assistant
  //                           id(sht30_temp).publish_state(temp);
  //                           id(sht30_hum).publish_state(hum);
  //   
  //                           ESP_LOGI("sht30_uart", "数据已发布到Home Assistant");
  //                         } else {
  //                           ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
  //                         }
  //                       } else {
  //                         ESP_LOGD("sht30_uart", "数据格式不匹配SHT30: %s", line.c_str());
  //                       }
  //   
  //                       id(uart_buffer) = "";
  //                     }
  //                   } else {
  //                     id(uart_buffer) += c;
  //                     if (id(uart_buffer).length() > 200) {
  //                       ESP_LOGW("sht30_uart", "缓冲区过长，清空");
  //                       id(uart_buffer) = "";
  //                     }
  //                   }
  //                 }
  //               } else {
  //                 ESP_LOGW("sht30_uart", "read_array返回0字节，但available显示有数据");
  //               }
  //             }
  //           }
  //         type_id: lambdaaction_id_4
  //     trigger_id: trigger_id_4
  //     automation_id: automation_id_4
  //     id: interval_intervaltrigger_id
  //     startup_delay: 0s
  //   - interval: 5s
  //     then:
  //       - uart.write:
  //           id: uart_bus
  //           data: !!binary |
  //             UkVBRA0K
  //         type_id: uart_uartwriteaction_id
  //     trigger_id: trigger_id_5
  //     automation_id: automation_id_5
  //     id: interval_intervaltrigger_id_2
  //     startup_delay: 0s
  interval_intervaltrigger_id = new interval::IntervalTrigger();
  interval_intervaltrigger_id->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id);
  automation_id_4 = new Automation<>(interval_intervaltrigger_id);
  // light.status_led:
  //   platform: status_led
  //   name: Status LED
  //   pin:
  //     number: 8
  //     mode:
  //       output: true
  //       input: false
  //       open_drain: false
  //       pullup: false
  //       pulldown: false
  //     id: esp32_esp32internalgpiopin_id_3
  //     inverted: false
  //     ignore_pin_validation_error: false
  //     ignore_strapping_warning: false
  //     drive_strength: 20.0
  //   disabled_by_default: false
  //   id: light_lightstate_id
  //   restore_mode: ALWAYS_OFF
  //   output_id: status_led_statusledlightoutput_id
  status_led_statusledlightoutput_id = new status_led::StatusLEDLightOutput();
  esp32_esp32internalgpiopin_id_3 = new esp32::ESP32InternalGPIOPin();
  esp32_esp32internalgpiopin_id_3->set_pin(::GPIO_NUM_8);
  esp32_esp32internalgpiopin_id_3->set_inverted(false);
  esp32_esp32internalgpiopin_id_3->set_drive_strength(::GPIO_DRIVE_CAP_2);
  esp32_esp32internalgpiopin_id_3->set_flags(gpio::Flags::FLAG_OUTPUT);
  status_led_statusledlightoutput_id->set_pin(esp32_esp32internalgpiopin_id_3);
  status_led_statusledlightoutput_id->set_component_source("status_led.light");
  App.register_component(status_led_statusledlightoutput_id);
  light_lightstate_id = new light::LightState(status_led_statusledlightoutput_id);
  App.register_light(light_lightstate_id);
  light_lightstate_id->set_component_source("light");
  App.register_component(light_lightstate_id);
  light_lightstate_id->set_name("Status LED");
  light_lightstate_id->set_object_id("status_led");
  light_lightstate_id->set_disabled_by_default(false);
  light_lightstate_id->set_restore_mode(light::LIGHT_ALWAYS_OFF);
  light_lightstate_id->add_effects({});
  // text_sensor.wifi_info:
  //   platform: wifi_info
  //   ip_address:
  //     name: IP Address
  //     disabled_by_default: false
  //     id: wifi_info_ipaddresswifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   ssid:
  //     name: Connected SSID
  //     disabled_by_default: false
  //     id: wifi_info_ssidwifiinfo_id
  //     entity_category: diagnostic
  //     update_interval: 1s
  //   mac_address:
  //     name: Mac Address
  //     disabled_by_default: false
  //     id: wifi_info_macaddresswifiinfo_id
  //     entity_category: diagnostic
  wifi_info_ssidwifiinfo_id = new wifi_info::SSIDWiFiInfo();
  App.register_text_sensor(wifi_info_ssidwifiinfo_id);
  wifi_info_ssidwifiinfo_id->set_name("Connected SSID");
  wifi_info_ssidwifiinfo_id->set_object_id("connected_ssid");
  wifi_info_ssidwifiinfo_id->set_disabled_by_default(false);
  wifi_info_ssidwifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ssidwifiinfo_id->set_update_interval(1000);
  wifi_info_ssidwifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ssidwifiinfo_id);
  wifi_info_macaddresswifiinfo_id = new wifi_info::MacAddressWifiInfo();
  App.register_text_sensor(wifi_info_macaddresswifiinfo_id);
  wifi_info_macaddresswifiinfo_id->set_name("Mac Address");
  wifi_info_macaddresswifiinfo_id->set_object_id("mac_address");
  wifi_info_macaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_macaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_macaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_macaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id = new wifi_info::IPAddressWiFiInfo();
  App.register_text_sensor(wifi_info_ipaddresswifiinfo_id);
  wifi_info_ipaddresswifiinfo_id->set_name("IP Address");
  wifi_info_ipaddresswifiinfo_id->set_object_id("ip_address");
  wifi_info_ipaddresswifiinfo_id->set_disabled_by_default(false);
  wifi_info_ipaddresswifiinfo_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  wifi_info_ipaddresswifiinfo_id->set_update_interval(1000);
  wifi_info_ipaddresswifiinfo_id->set_component_source("wifi_info.text_sensor");
  App.register_component(wifi_info_ipaddresswifiinfo_id);
  // binary_sensor.status:
  //   platform: status
  //   name: Status
  //   disabled_by_default: false
  //   id: status_statusbinarysensor_id
  //   entity_category: diagnostic
  //   device_class: connectivity
  status_statusbinarysensor_id = new status::StatusBinarySensor();
  App.register_binary_sensor(status_statusbinarysensor_id);
  status_statusbinarysensor_id->set_name("Status");
  status_statusbinarysensor_id->set_object_id("status");
  status_statusbinarysensor_id->set_disabled_by_default(false);
  status_statusbinarysensor_id->set_entity_category(::ENTITY_CATEGORY_DIAGNOSTIC);
  status_statusbinarysensor_id->set_device_class("connectivity");
  status_statusbinarysensor_id->set_trigger_on_initial_state(false);
  status_statusbinarysensor_id->set_component_source("status.binary_sensor");
  App.register_component(status_statusbinarysensor_id);
  // switch.restart:
  //   platform: restart
  //   name: Restart
  //   disabled_by_default: false
  //   restore_mode: ALWAYS_OFF
  //   id: restart_restartswitch_id
  //   entity_category: config
  //   icon: mdi:restart
  restart_restartswitch_id = new restart::RestartSwitch();
  App.register_switch(restart_restartswitch_id);
  restart_restartswitch_id->set_name("Restart");
  restart_restartswitch_id->set_object_id("restart");
  restart_restartswitch_id->set_disabled_by_default(false);
  restart_restartswitch_id->set_icon("mdi:restart");
  restart_restartswitch_id->set_entity_category(::ENTITY_CATEGORY_CONFIG);
  restart_restartswitch_id->set_restore_mode(switch_::SWITCH_ALWAYS_OFF);
  restart_restartswitch_id->set_component_source("restart.switch");
  App.register_component(restart_restartswitch_id);
  // output:
  // md5:
  // socket:
  //   implementation: bsd_sockets
  // globals:
  //   id: uart_buffer
  //   type: std::string
  //   restore_value: false
  //   initial_value: '""'
  uart_buffer = new globals::GlobalsComponent<std::string>("");
  uart_buffer->set_component_source("globals");
  App.register_component(uart_buffer);
  lambdaaction_id_4 = new LambdaAction<>([=]() -> void {
      #line 86 "sht30-esp32c3.yaml"
       
      int available = uart_bus->available();
      if (available > 0) {
        ESP_LOGI("sht30_uart", "UART缓冲区有 %d 字节可用数据", available);
      
         
        uart_bus->flush();
      
         
        delay(50);
      
         
        available = uart_bus->available();
        ESP_LOGI("sht30_uart", "flush后UART缓冲区有 %d 字节可用数据", available);
      
        if (available > 0) {
           
          uint8_t peek_data;
          if (uart_bus->peek_byte(&peek_data)) {
            ESP_LOGI("sht30_uart", "peek到第一个字节: 0x%02X ('%c')", peek_data,
                     (peek_data >= 32 && peek_data <= 126) ? (char)peek_data : '?');
          }
      
           
          uint8_t data[64];   
          size_t len = uart_bus->read_array(data, std::min(available, 64));
      
          ESP_LOGI("sht30_uart", "实际读取到 %d 字节数据", len);
      
          if (len > 0) {
             
            std::string hex_data = "";
            std::string readable = "";
      
            for (size_t i = 0; i < len; i++) {
              char hex_char[4];
              sprintf(hex_char, "%02X ", data[i]);
              hex_data += hex_char;
      
              char c = (char)data[i];
              if (c >= 32 && c <= 126) {
                readable += c;
              } else if (c == '\r') {
                readable += "\\r";
              } else if (c == '\n') {
                readable += "\\n";
              } else {
                char ctrl_char[6];
                sprintf(ctrl_char, "\\x%02X", (uint8_t)c);
                readable += ctrl_char;
              }
            }
      
            ESP_LOGI("sht30_uart", "原始数据(HEX): %s", hex_data.c_str());
            ESP_LOGI("sht30_uart", "可读数据: %s", readable.c_str());
      
             
            for (size_t i = 0; i < len; i++) {
              char c = (char)data[i];
              if (c == '\n' || c == '\r') {
                if (!uart_buffer->value().empty()) {
                  std::string line = uart_buffer->value();
                  ESP_LOGI("sht30_uart", "完整行数据: '%s' (长度: %d)", line.c_str(), line.length());
      
                   
                  if (line.find(":") == 0 && line.find("RH") != std::string::npos && line.find("C") != std::string::npos) {
                    ESP_LOGI("sht30_uart", "检测到SHT30数据格式，开始解析...");
      
                     
                     
                     
      
                    bool parse_ok = true;
                    float hum = 0.0, temp = 0.0;
      
                     
                    if (line.length() >= 6) {
                      std::string hum_str = line.substr(1, 5);   
                      char* hum_end;
                      hum = strtof(hum_str.c_str(), &hum_end);
                      if (hum_end == hum_str.c_str()) parse_ok = false;
                    } else {
                      parse_ok = false;
                    }
      
                     
                    if (parse_ok && line.length() >= 14) {
                      std::string temp_str = line.substr(9, 5);   
                      char* temp_end;
                      temp = strtof(temp_str.c_str(), &temp_end);
                      if (temp_end == temp_str.c_str()) parse_ok = false;
                    } else {
                      parse_ok = false;
                    }
      
                    if (parse_ok) {
                      ESP_LOGI("sht30_uart", "解析成功 - 湿度: %.1f%%, 温度: %.1f°C", hum, temp);
      
                       
                      sht30_temp->publish_state(temp);
                      sht30_hum->publish_state(hum);
      
                      ESP_LOGI("sht30_uart", "数据已发布到Home Assistant");
                    } else {
                      ESP_LOGW("sht30_uart", "解析失败: %s", line.c_str());
                    }
                  } else {
                    ESP_LOGD("sht30_uart", "数据格式不匹配SHT30: %s", line.c_str());
                  }
      
                  uart_buffer->value() = "";
                }
              } else {
                uart_buffer->value() += c;
                if (uart_buffer->value().length() > 200) {
                  ESP_LOGW("sht30_uart", "缓冲区过长，清空");
                  uart_buffer->value() = "";
                }
              }
            }
          } else {
            ESP_LOGW("sht30_uart", "read_array返回0字节，但available显示有数据");
          }
        }
      }
  });
  automation_id_4->add_actions({lambdaaction_id_4});
  interval_intervaltrigger_id->set_update_interval(500);
  interval_intervaltrigger_id->set_startup_delay(0);
  interval_intervaltrigger_id_2 = new interval::IntervalTrigger();
  interval_intervaltrigger_id_2->set_component_source("interval");
  App.register_component(interval_intervaltrigger_id_2);
  automation_id_5 = new Automation<>(interval_intervaltrigger_id_2);
  uart_uartwriteaction_id = new uart::UARTWriteAction<>();
  uart_uartwriteaction_id->set_parent(uart_bus);
  uart_uartwriteaction_id->set_data_static({82, 69, 65, 68, 13, 10});
  automation_id_5->add_actions({uart_uartwriteaction_id});
  interval_intervaltrigger_id_2->set_update_interval(5000);
  interval_intervaltrigger_id_2->set_startup_delay(0);
  // =========== AUTO GENERATED CODE END ============
  App.setup();
}

void loop() {
  App.loop();
}
