#pragma once
#include "esphome/core/macros.h"
#define ESPHOME_BOARD "nodemcuv2"
#define ESPHOME_VARIANT "ESP8266"
#define USE_API
#define USE_API_PLAINTEXT
#define USE_ARDUINO_VERSION_CODE VERSION_CODE(3, 1, 2)
#define USE_ESP8266_EARLY_PIN_INIT
#define USE_LOGGER
#define USE_MD5
#define USE_MDNS
#define USE_NETWORK
#define USE_NETWORK_IPV6 false
#define USE_OTA
#define USE_OTA_VERSION 2
#define USE_SENSOR
#define USE_SOCKET_IMPL_LWIP_TCP
#define USE_WIFI
