esphome:
  name: myesp32c3
  friendly_name: "SHT30 ESP32-C3 Sensor"

esp32:
  board: airm2m_core_esp32c3
  framework:
    type: arduino

# 从secrets.yaml文件中读取WiFi配置
wifi:
  ssid: !secret wifi_ssid
  password: !secret wifi_password
  
  # WiFi连接失败时的处理
  ap:
    ssid: "ESP32C3-SHT30 Fallback Hotspot"
    password: "12345678"

  # WiFi连接状态回调
  on_connect:
    - logger.log: "WiFi连接成功！"
  on_disconnect:
    - logger.log: "WiFi连接断开！"

# UART配置 - 用于读取SHT30传感器数据
uart:
  id: uart_bus
  tx_pin: GPIO0  # TX引脚 - 如果需要发送命令到传感器
  rx_pin: GPIO1  # RX引脚 - 接收传感器数据
  baud_rate: 9600  # 尝试更高的波特率
  data_bits: 8
  parity: NONE
  stop_bits: 1
  rx_buffer_size: 512  # 增加接收缓冲区大小
  # debug:  # 关闭UART调试以减少日志输出
  #   direction: RX
  #   dummy_receiver: false

# 全局变量存储UART数据缓冲区和上次数值
globals:
  - id: uart_buffer
    type: std::string
    restore_value: no
    initial_value: '""'
  - id: last_temperature
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的温度
  - id: last_humidity
    type: float
    restore_value: no
    initial_value: '-999.0'  # 初始值设为不可能的湿度

sensor:
  # SHT30 UART传感器 - 温度
  - platform: template
    name: "SHT30 UART Temperature"
    id: sht30_temp
    unit_of_measurement: "°C"
    device_class: temperature
    state_class: measurement
    accuracy_decimals: 1

  # SHT30 UART传感器 - 湿度
  - platform: template
    name: "SHT30 UART Humidity"
    id: sht30_hum
    unit_of_measurement: "%"
    device_class: humidity
    state_class: measurement
    accuracy_decimals: 1

  # WiFi信号强度传感器
  - platform: wifi_signal
    name: "WiFi Signal"
    update_interval: 60s

  # 设备运行时间传感器
  - platform: uptime
    name: "Uptime"
    update_interval: 60s

  # ESP32-C3内部温度传感器
  - platform: internal_temperature
    name: "ESP32-C3 Internal Temperature"
    update_interval: 60s

# UART数据处理间隔
interval:
  - interval: 100ms  # 每100ms检查一次UART数据
    then:
      - lambda: |-
          // 检查UART是否有可用数据
          int available = id(uart_bus).available();
          if (available > 0) {
            // 简化日志输出，只在调试时显示详细信息
            // ESP_LOGD("sht30_uart", "UART缓冲区有 %d 字节可用数据", available);

            // 使用数组读取方式
            uint8_t data[256];
            std::string received_data = "";

            // 尝试读取所有可用数据
            int bytes_read = 0;
            uint8_t buffer[1];

            // 循环读取单个字节
            while (id(uart_bus).available() && bytes_read < 256) {
              if (id(uart_bus).read_array(buffer, 1) > 0) {
                received_data += (char)buffer[0];
                bytes_read++;
              } else {
                break;
              }
            }

            // 处理接收到的数据行
            for (char c : received_data) {
              if (c == '\n' || c == '\r') {
                if (!id(uart_buffer).empty()) {
                  std::string line = id(uart_buffer);
                  // ESP_LOGD("sht30_uart", "完整行数据: '%s'", line.c_str());

                  // 解析SHT30数据格式: :068.9RH 031.6C
                  if (line.find(":") == 0 && line.find("RH") != std::string::npos && line.find("C") != std::string::npos) {
                    // ESP_LOGD("sht30_uart", "检测到SHT30数据格式，开始解析");

                    // 查找湿度数据 (RH前的数字)
                    size_t rh_pos = line.find("RH");
                    if (rh_pos != std::string::npos && rh_pos > 1) {
                      // 从冒号后开始查找湿度值
                      std::string humidity_str = line.substr(1, rh_pos - 1);
                      float humidity = atof(humidity_str.c_str());

                      // 湿度极端值过滤 (0-100%)
                      if (humidity >= 0.0 && humidity <= 100.0) {
                        // 检查数值变化是否足够大 (变化超过0.5%才更新)
                        if (id(last_humidity) == -999.0 || abs(humidity - id(last_humidity)) >= 0.5) {
                          ESP_LOGI("sht30_uart", "湿度: %.1f%%", humidity);
                          id(sht30_hum).publish_state(humidity);
                          id(last_humidity) = humidity;
                        }
                      } else {
                        ESP_LOGW("sht30_uart", "湿度异常值被过滤 - 原始数据: '%s', 解析值: %.1f%%, 有效范围: 0-100%%", line.c_str(), humidity);
                      }
                    }

                    // 查找温度数据 (C前的数字)
                    size_t c_pos = line.find("C");
                    size_t space_pos = line.find(" ");
                    if (c_pos != std::string::npos && space_pos != std::string::npos && c_pos > space_pos) {
                      // 从空格后开始查找温度值
                      std::string temp_str = line.substr(space_pos + 1, c_pos - space_pos - 1);
                      float temperature = atof(temp_str.c_str());

                      // 温度极端值过滤 (-40°C 到 80°C)
                      if (temperature >= -40.0 && temperature <= 80.0) {
                        // 检查数值变化是否足够大 (变化超过0.2°C才更新)
                        if (id(last_temperature) == -999.0 || abs(temperature - id(last_temperature)) >= 0.2) {
                          ESP_LOGI("sht30_uart", "温度: %.1f°C", temperature);
                          id(sht30_temp).publish_state(temperature);
                          id(last_temperature) = temperature;
                        }
                      } else {
                        ESP_LOGW("sht30_uart", "温度异常值被过滤 - 原始数据: '%s', 解析值: %.1f°C, 有效范围: -40~80°C", line.c_str(), temperature);
                      }
                    }
                  } else if (!line.empty()) {
                    // 记录无法解析的数据格式
                    ESP_LOGW("sht30_uart", "无法解析的数据格式 - 原始数据: '%s'", line.c_str());
                  }

                  id(uart_buffer) = "";
                }
              } else {
                id(uart_buffer) += c;
                if (id(uart_buffer).length() > 200) {
                  id(uart_buffer) = "";
                }
              }
            }
          }

  # 可选：定期发送读取命令到传感器
  - interval: 5s
    then:
      - uart.write:
          id: uart_bus
          data: "READ\r\n"  # 发送读取命令到传感器（如果传感器需要命令触发）

# 状态LED指示
light:
  - platform: status_led
    name: "Status LED"
    pin: GPIO8  # ESP32-C3开发板上的LED引脚

# 文本传感器
text_sensor:
  # WiFi信息
  - platform: wifi_info
    ip_address:
      name: "IP Address"
    ssid:
      name: "Connected SSID"
    mac_address:
      name: "Mac Address"

# 二进制传感器
binary_sensor:
  # 设备状态
  - platform: status
    name: "Status"

# 开关控制
switch:
  # 重启开关
  - platform: restart
    name: "Restart"

# 日志配置
logger:
  baud_rate: 115200
  level: INFO  # 只显示重要信息
  logs:
    sht30_uart: INFO  # 只显示传感器数据
    uart_debug: WARN  # 减少UART调试信息
    uart: WARN        # 减少UART日志
    wifi: WARN        # 减少WiFi日志
    api: WARN         # 减少API日志
    ota: WARN         # 减少OTA日志
    web_server: WARN  # 减少Web服务器日志

# OTA更新
ota:
  platform: esphome

# API配置
api:

# Web服务器
web_server:
  port: 80
  auth:
    username: admin
    password: !secret wifi_password
