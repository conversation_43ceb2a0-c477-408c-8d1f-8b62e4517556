{"storage_version": 1, "name": "myesphome8266", "friendly_name": null, "comment": null, "esphome_version": "2025.7.2", "src_version": 1, "address": "myesphome8266.local", "web_port": 80, "esp_platform": "ESP8266", "build_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesphome8266", "firmware_bin_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesphome8266\\.pioenvs\\myesphome8266\\firmware.bin", "loaded_integrations": ["api", "async_tcp", "dht", "esp8266", "esphome", "json", "logger", "md5", "mdns", "network", "ota", "preferences", "safe_mode", "sensor", "socket", "web_server", "web_server_base", "wifi"], "loaded_platforms": ["ota/esphome", "sensor/dht"], "no_mdns": false, "framework": "a<PERSON><PERSON><PERSON>", "core_platform": "esp8266"}