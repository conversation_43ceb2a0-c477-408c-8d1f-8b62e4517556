{"build_type": "release", "env_name": "myesp8266", "libsource_dirs": ["C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\lib", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266", "C:\\Users\\<USER>\\.platformio\\lib", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries"], "defines": ["PLATFORMIO=60118", "ESP8266", "ARDUINO_ARCH_ESP8266", "ARDUINO_ESP8266_NODEMCU_ESP12E", "ESPHOME_LOG_LEVEL=ESPHOME_LOG_LEVEL_DEBUG", "NEW_OOM_ABORT", "PIO_FRAMEWORK_ARDUINO_LWIP2_HIGHER_BANDWIDTH_LOW_FLASH", "USE_ARDUINO", "USE_ESP8266", "USE_ESP8266_FRAMEWORK_ARDUINO", "USE_STORE_LOG_STR_IN_FLASH", "F_CPU=80000000L", "__ets__", "ICACHE_FLASH", "_GNU_SOURCE", "ARDUINO=10805", "ARDUINO_BOARD=\"PLATFORMIO_NODEMCUV2\"", "ARDUINO_BOARD_ID=\"nodemcuv2\"", "FLASHMODE_DOUT", "LWIP_OPEN_SRC", "NONOSDK22x_190703=1", "TCP_MSS=1460", "LWIP_FEATURES=0", "LWIP_IPV6=0", "VTABLES_IN_FLASH", "MMU_IRAM_SIZE=0x8000", "MMU_ICACHE_SIZE=0x8000"], "includes": {"build": ["C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ArduinoJson\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266mDNS\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncWebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Hash\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WiFi\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncTCP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\tools\\sdk\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\cores\\esp8266", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\tools\\sdk\\lwip2\\include", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\variants\\nodemcu"], "compatlib": ["C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ArduinoJson\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266mDNS\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncWebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Hash\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncTCP\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncTCP\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncWebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WiFi\\src", "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.piolibdeps\\myesp8266\\ESPAsyncTCP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Hash\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WiFi\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266mDNS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Hash\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ArduinoOTA", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\DNSServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\EEPROM", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266AVRISP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266HTTPClient\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266HTTPUpdateServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266LLMNR", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266NetBIOS", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266SSDP", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266SdFat\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WebServer\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266WiFiMesh\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\ESP8266httpUpdate\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\FSTools", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\GDBStub\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\I2S\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\LittleFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Netdump\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\SD\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\SDFS\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\SPI", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\SPISlave\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Servo\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\SoftwareSerial\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\TFT_Touch_Shield_V2", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Ticker\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\Wire", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\esp8266\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\lwIP_Ethernet\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\lwIP_PPP\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\lwIP_enc28j60\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\lwIP_w5100\\src", "C:\\Users\\<USER>\\.platformio\\packages\\framework-arduinoespressif8266\\libraries\\lwIP_w5500\\src"], "toolchain": ["C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\xtensa-lx106-elf\\include\\c++\\10.3.0", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\xtensa-lx106-elf\\include\\c++\\10.3.0\\xtensa-lx106-elf", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\lib\\gcc\\xtensa-lx106-elf\\10.3.0\\include", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\lib\\gcc\\xtensa-lx106-elf\\10.3.0\\include-fixed", "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\xtensa-lx106-elf\\include"]}, "cc_flags": ["-std=gnu17", "-<PERSON><PERSON><PERSON>-<PERSON><PERSON>", "-Wno-implicit-function-declaration", "-Wl,-E<PERSON>", "-fno-inline-functions", "-nostdlib", "-Wno-nonnull-compare", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions", "-<PERSON><PERSON>", "-mlongcalls", "-mtext-section-literals", "-falign-functions=4", "-U__STRICT_ANSI__", "-ffunction-sections", "-fdata-sections", "-Wall", "-Werror=return-type", "-free", "-fipa-pta"], "cxx_flags": ["-Wno-volatile", "-std=gnu++20", "-fno-rtti", "-fno-exceptions", "-Wno-nonnull-compare", "-Wno-sign-compare", "-Wno-unused-but-set-variable", "-Wno-unused-variable", "-fno-exceptions", "-<PERSON><PERSON>", "-mlongcalls", "-mtext-section-literals", "-falign-functions=4", "-U__STRICT_ANSI__", "-ffunction-sections", "-fdata-sections", "-Wall", "-Werror=return-type", "-free", "-fipa-pta"], "cc_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\bin\\xtensa-lx106-elf-gcc.exe", "cxx_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\bin\\xtensa-lx106-elf-g++.exe", "gdb_path": "C:\\Users\\<USER>\\.platformio\\packages\\toolchain-xtensa\\bin\\xtensa-lx106-elf-gdb.exe", "prog_path": "C:\\git-program\\Embedded\\MY-ESPHOME\\.esphome\\build\\myesp8266\\.pioenvs\\myesp8266\\firmware.elf", "svd_path": null, "compiler_type": "gcc", "targets": [{"name": "buildfs", "title": "Build Filesystem Image", "description": null, "group": "Platform"}, {"name": "size", "title": "Program Size", "description": "Calculate program size", "group": "Platform"}, {"name": "upload", "title": "Upload", "description": null, "group": "Platform"}, {"name": "uploadfs", "title": "Upload Filesystem Image", "description": null, "group": "Platform"}, {"name": "uploadfsota", "title": "Upload Filesystem Image OTA", "description": null, "group": "Platform"}, {"name": "erase", "title": "Erase Flash", "description": null, "group": "Platform"}], "extra": {"flash_images": []}}